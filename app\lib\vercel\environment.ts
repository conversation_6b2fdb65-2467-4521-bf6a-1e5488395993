/**
 * Vercel Environment Detection and Optimization Utilities
 * 
 * This module provides utilities for detecting Vercel deployment environment
 * and optimizing application behavior accordingly.
 */

export interface VercelEnvironment {
  isVercel: boolean;
  isProduction: boolean;
  isDevelopment: boolean;
  isPreview: boolean;
  region?: string;
  deploymentUrl?: string;
  gitCommitSha?: string;
  gitCommitRef?: string;
}

/**
 * Detect current Vercel environment
 */
export function getVercelEnvironment(env?: Record<string, string | undefined>): VercelEnvironment {
  const environment = env || (typeof process !== "undefined" ? process.env : {});
  
  const isVercel = environment.VERCEL === "1";
  const nodeEnv = environment.NODE_ENV;
  const vercelEnv = environment.VERCEL_ENV;
  
  return {
    isVercel,
    isProduction: nodeEnv === "production" || vercelEnv === "production",
    isDevelopment: nodeEnv === "development" || vercelEnv === "development",
    isPreview: vercelEnv === "preview",
    region: environment.VERCEL_REGION,
    deploymentUrl: environment.VERCEL_URL,
    gitCommitSha: environment.VERCEL_GIT_COMMIT_SHA,
    gitCommitRef: environment.VERCEL_GIT_COMMIT_REF,
  };
}

/**
 * Get optimized configuration for Vercel serverless functions
 */
export function getVercelOptimizedConfig(env?: Record<string, string | undefined>) {
  const vercelEnv = getVercelEnvironment(env);
  
  return {
    // Database connection settings
    database: {
      connectionTimeoutMs: vercelEnv.isVercel ? 10000 : 30000,
      idleTimeoutMs: vercelEnv.isVercel ? 300000 : 600000,
      maxConnections: vercelEnv.isVercel ? 5 : 10,
      enableLogging: vercelEnv.isDevelopment,
    },
    
    // Cache settings
    cache: {
      defaultTtl: vercelEnv.isProduction ? 3600 : 300, // 1 hour in prod, 5 min in dev
      maxAge: vercelEnv.isProduction ? 86400 : 3600, // 24 hours in prod, 1 hour in dev
    },
    
    // API settings
    api: {
      timeout: vercelEnv.isVercel ? 25000 : 60000, // 25s for Vercel (under 30s limit)
      retries: vercelEnv.isVercel ? 2 : 3,
      rateLimit: vercelEnv.isProduction ? 100 : 1000, // requests per minute
    },
    
    // Logging settings
    logging: {
      level: vercelEnv.isProduction ? "warn" : "debug",
      enableConsole: true,
      enableFile: !vercelEnv.isVercel, // No file logging in serverless
    },
    
    // Security settings
    security: {
      enableCors: true,
      enableHelmet: vercelEnv.isProduction,
      enableRateLimit: vercelEnv.isProduction,
      trustProxy: vercelEnv.isVercel,
    },
  };
}

/**
 * Check if we're running in a Vercel serverless function
 */
export function isVercelServerless(env?: Record<string, string | undefined>): boolean {
  const environment = env || (typeof process !== "undefined" ? process.env : {});
  return environment.VERCEL === "1" && environment.AWS_LAMBDA_FUNCTION_NAME !== undefined;
}

/**
 * Get Vercel deployment information
 */
export function getVercelDeploymentInfo(env?: Record<string, string | undefined>) {
  const environment = env || (typeof process !== "undefined" ? process.env : {});
  
  if (environment.VERCEL !== "1") {
    return null;
  }
  
  return {
    url: environment.VERCEL_URL,
    region: environment.VERCEL_REGION,
    env: environment.VERCEL_ENV,
    gitCommitSha: environment.VERCEL_GIT_COMMIT_SHA,
    gitCommitRef: environment.VERCEL_GIT_COMMIT_REF,
    gitCommitMessage: environment.VERCEL_GIT_COMMIT_MESSAGE,
    gitRepoOwner: environment.VERCEL_GIT_REPO_OWNER,
    gitRepoSlug: environment.VERCEL_GIT_REPO_SLUG,
    projectId: environment.VERCEL_PROJECT_ID,
    teamId: environment.VERCEL_TEAM_ID,
  };
}

/**
 * Get optimized headers for Vercel deployment
 */
export function getVercelOptimizedHeaders(env?: Record<string, string | undefined>) {
  const vercelEnv = getVercelEnvironment(env);
  
  const headers: Record<string, string> = {};
  
  if (vercelEnv.isProduction) {
    // Security headers for production
    headers["X-Content-Type-Options"] = "nosniff";
    headers["X-Frame-Options"] = "DENY";
    headers["X-XSS-Protection"] = "1; mode=block";
    headers["Referrer-Policy"] = "strict-origin-when-cross-origin";
    headers["Permissions-Policy"] = "camera=(), microphone=(), geolocation=()";
  }
  
  if (vercelEnv.isVercel) {
    // Vercel-specific headers
    headers["X-Powered-By"] = "Vercel";
    if (vercelEnv.region) {
      headers["X-Vercel-Region"] = vercelEnv.region;
    }
    if (vercelEnv.gitCommitSha) {
      headers["X-Git-Commit-SHA"] = vercelEnv.gitCommitSha;
    }
  }
  
  return headers;
}

/**
 * Log deployment information (useful for debugging)
 */
export function logVercelDeploymentInfo(env?: Record<string, string | undefined>) {
  const deploymentInfo = getVercelDeploymentInfo(env);
  const vercelEnv = getVercelEnvironment(env);
  
  if (deploymentInfo) {
    console.log("🚀 Vercel Deployment Info:", {
      environment: vercelEnv.isProduction ? "production" : vercelEnv.isPreview ? "preview" : "development",
      url: deploymentInfo.url,
      region: deploymentInfo.region,
      commit: deploymentInfo.gitCommitSha?.substring(0, 7),
      branch: deploymentInfo.gitCommitRef,
    });
  } else if (vercelEnv.isDevelopment) {
    console.log("🔧 Development Environment Detected");
  }
}
