#!/usr/bin/env tsx

/**
 * Database Migration Script
 * 
 * This script provides utilities for running database migrations safely
 * in both development and production environments.
 */

import { readFileSync, existsSync } from "fs";
import { join } from "path";
import { neon } from "@neondatabase/serverless";

interface MigrationConfig {
  databaseUrl: string;
  migrationsDir: string;
  dryRun: boolean;
  verbose: boolean;
}

class MigrationRunner {
  private sql: any;
  private config: MigrationConfig;

  constructor(config: MigrationConfig) {
    this.config = config;
    this.sql = neon(config.databaseUrl);
  }

  /**
   * Run a specific migration file
   */
  async runMigration(migrationFile: string): Promise<void> {
    const migrationPath = join(this.config.migrationsDir, migrationFile);
    
    if (!existsSync(migrationPath)) {
      throw new Error(`Migration file not found: ${migrationPath}`);
    }

    const migrationSql = readFileSync(migrationPath, "utf-8");
    
    if (this.config.verbose) {
      console.log(`📄 Running migration: ${migrationFile}`);
      if (this.config.dryRun) {
        console.log("🔍 SQL to be executed:");
        console.log(migrationSql);
        return;
      }
    }

    if (this.config.dryRun) {
      console.log(`✅ Dry run: Would execute ${migrationFile}`);
      return;
    }

    try {
      // Split SQL by semicolons and execute each statement
      const statements = migrationSql
        .split(";")
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0);

      for (const statement of statements) {
        if (this.config.verbose) {
          console.log(`🔧 Executing: ${statement.substring(0, 100)}...`);
        }
        await this.sql(statement);
      }

      console.log(`✅ Migration completed: ${migrationFile}`);
    } catch (error) {
      console.error(`❌ Migration failed: ${migrationFile}`);
      throw error;
    }
  }

  /**
   * Check database connection
   */
  async checkConnection(): Promise<void> {
    try {
      await this.sql`SELECT 1`;
      console.log("✅ Database connection successful");
    } catch (error) {
      console.error("❌ Database connection failed:", error);
      throw error;
    }
  }

  /**
   * Create migrations table if it doesn't exist
   */
  async ensureMigrationsTable(): Promise<void> {
    try {
      await this.sql`
        CREATE TABLE IF NOT EXISTS __drizzle_migrations (
          id SERIAL PRIMARY KEY,
          hash TEXT NOT NULL,
          created_at TIMESTAMP DEFAULT NOW()
        )
      `;
      console.log("✅ Migrations table ready");
    } catch (error) {
      console.error("❌ Failed to create migrations table:", error);
      throw error;
    }
  }
}

/**
 * Load environment variables
 */
function loadEnvVars(): Record<string, string> {
  const envVars: Record<string, string> = {};

  // Load from .env file
  const envPath = join(process.cwd(), ".env");
  if (existsSync(envPath)) {
    const envContent = readFileSync(envPath, "utf-8");
    const vars = envContent.split("\n").reduce((acc, line) => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith("#")) {
        const [key, ...valueParts] = trimmedLine.split("=");
        if (key && valueParts.length > 0) {
          let value = valueParts.join("=").trim();
          if (
            (value.startsWith('"') && value.endsWith('"')) ||
            (value.startsWith("'") && value.endsWith("'"))
          ) {
            value = value.slice(1, -1);
          }
          acc[key.trim()] = value;
        }
      }
      return acc;
    }, {} as Record<string, string>);

    Object.assign(envVars, vars);
  }

  return envVars;
}

/**
 * Main migration function
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const migrationFile = args[1];

  // Parse flags
  const dryRun = args.includes("--dry-run");
  const verbose = args.includes("--verbose");

  // Load environment variables
  const envVars = loadEnvVars();
  Object.assign(process.env, envVars);

  const databaseUrl = process.env.DATABASE_URL;
  if (!databaseUrl) {
    console.error("❌ DATABASE_URL environment variable is required");
    process.exit(1);
  }

  const config: MigrationConfig = {
    databaseUrl,
    migrationsDir: join(process.cwd(), "drizzle"),
    dryRun,
    verbose,
  };

  const runner = new MigrationRunner(config);

  try {
    console.log("🚀 Starting migration process...");
    
    if (dryRun) {
      console.log("🔍 Running in dry-run mode (no changes will be made)");
    }

    await runner.checkConnection();
    await runner.ensureMigrationsTable();

    switch (command) {
      case "run":
        if (!migrationFile) {
          console.error("❌ Migration file is required for 'run' command");
          console.log("Usage: tsx scripts/migrate.ts run <migration-file> [--dry-run] [--verbose]");
          process.exit(1);
        }
        await runner.runMigration(migrationFile);
        break;

      case "add-missing-fields":
        await runner.runMigration("0001_add_missing_fields.sql");
        break;

      case "rollback-missing-fields":
        await runner.runMigration("0001_add_missing_fields_rollback.sql");
        break;

      default:
        console.log("Available commands:");
        console.log("  run <file>              - Run a specific migration file");
        console.log("  add-missing-fields      - Add missing database fields");
        console.log("  rollback-missing-fields - Rollback missing fields migration");
        console.log("");
        console.log("Flags:");
        console.log("  --dry-run              - Show what would be executed without making changes");
        console.log("  --verbose              - Show detailed output");
        console.log("");
        console.log("Examples:");
        console.log("  tsx scripts/migrate.ts add-missing-fields --dry-run");
        console.log("  tsx scripts/migrate.ts run 0001_add_missing_fields.sql --verbose");
        break;
    }

    console.log("🎉 Migration process completed successfully");
  } catch (error) {
    console.error("💥 Migration process failed:", error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}
